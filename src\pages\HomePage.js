import React from "react";
import {
  FaFacebookF,
  FaTwi<PERSON>,
  Fa<PERSON>nstagram,
  FaLinkedinIn,
} from "react-icons/fa";

const HomePage = () => {
  return (
    <div
      style={{
        display: "flex",
        minHeight: "100vh",
        backgroundColor: "#07272E",
        paddingLeft: "100px",
        paddingRight: "20px",
        paddingBottom: "80px",
      }}
    >
      <div
        style={{
          width: "5%",
          height: "calc(100vh - 70px)",
          display: "flex",
          flexDirection: "column",
          alignItems: "end",
          justifyContent: "space-between",
          paddingTop: "200px",
          paddingBottom: "80px",
          borderRight: "1px solid #fff",
        }}
      >
        <div style={{ display: "flex", flexDirection: "column", gap: "400px" , width: "80%" }}>
          <div
            style={{
              fontSize: "18px",
              transform: "rotate(-90deg)",
              whiteSpace: "nowrap",
              color: "#CDFF9A",
            }}
          >
            2023 Dotcreativemarket
          </div>

          <div
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: "20px",
            }}
          >
            <div
              style={{
                fontSize: "18px",
                transform: "rotate(-90deg)",
                whiteSpace: "nowrap",
                marginBottom: "20px",
                color: "#CDFF9A",
              }}
            >
              FOLLOW US
            </div>
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                gap: "20px",
                alignItems: "center",
              }}
            >
              <FaFacebookF
                style={{
                  color: "#ffffff",
                  fontSize: "18px",
                  cursor: "pointer",
                  transition: "color 0.3s ease",
                }}
                onMouseEnter={(e) => (e.target.style.color = "#4267B2")}
                onMouseLeave={(e) => (e.target.style.color = "#ffffff")}
              />
              <FaTwitter
                style={{
                  color: "#ffffff",
                  fontSize: "18px",
                  cursor: "pointer",
                  transition: "color 0.3s ease",
                }}
                onMouseEnter={(e) => (e.target.style.color = "#1DA1F2")}
                onMouseLeave={(e) => (e.target.style.color = "#ffffff")}
              />
              <FaInstagram
                style={{
                  color: "#ffffff",
                  fontSize: "18px",
                  cursor: "pointer",
                  transition: "color 0.3s ease",
                }}
                onMouseEnter={(e) => (e.target.style.color = "#E4405F")}
                onMouseLeave={(e) => (e.target.style.color = "#ffffff")}
              />
              <FaLinkedinIn
                style={{
                  color: "#ffffff",
                  fontSize: "18px",
                  cursor: "pointer",
                  transition: "color 0.3s ease",
                }}
                onMouseEnter={(e) => (e.target.style.color = "#0077B5")}
                onMouseLeave={(e) => (e.target.style.color = "#ffffff")}
              />
            </div>
          </div>
        </div>
      </div>

      <div
        style={{
          width: "40%",
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          paddingLeft: "60px",
          position: "relative",
        }}
      >
        <div
          style={{
            position: "absolute",
            fontSize: "400px",
            fontWeight: "bold",
            color: "rgba(109, 139, 116, 0.3)",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            zIndex: 1,
            lineHeight: "1",
            fontFamily: "Arial, sans-serif",
          }}
        >
          01
        </div>

        <div style={{ position: "relative", zIndex: 2 }}>
          <div
            style={{
              fontSize: "16px",
              color: "#CDFF9A",
              marginBottom: "30px",
              letterSpacing: "2px",
              fontWeight: "400",
            }}
          >
            BEGIN BREAKING YOUR LIMIT
          </div>

          <h1
            style={{
              fontSize: "64px",
              fontWeight: "bold",
              color: "#ffffff",
              lineHeight: "1.1",
              marginBottom: "50px",
              fontFamily: "Arial, sans-serif",
            }}
          >
            KNOW YOUR
            <br />
            LIMITS. SKI
            <br />
            BEYOND THEM.
          </h1>

          <button
            style={{
              backgroundColor: "transparent",
              border: "2px solid #CDFF9A",
              color: "#CDFF9A",
              padding: "15px 30px",
              fontSize: "14px",
              letterSpacing: "1px",
              cursor: "pointer",
              transition: "all 0.3s ease",
              fontWeight: "500",
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = "#CDFF9A";
              e.target.style.color = "#1a3d2e";
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = "transparent";
              e.target.style.color = "#CDFF9A";
            }}
          >
            VIEW MORE
          </button>
        </div>
      </div>
      <div style={{ width: "50%" }}></div>
    </div>
  );
};

export default HomePage;
